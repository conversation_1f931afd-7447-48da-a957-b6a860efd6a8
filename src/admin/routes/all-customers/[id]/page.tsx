import { useState, useEffect } from "react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  User,
  ArrowLeft,
  Mail,
  Phone,
  Calendar,
  Users,
  Bed,
  Edit,
  UserPlus,
  Copy,
} from "lucide-react";
import { Container, Heading, Text, Button, Badge, Tabs, toast } from "@camped-ai/ui";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import AddFamilyMemberDrawer from "../../../components/customer/AddFamilyMemberDrawer";
import EditCustomerDrawer from "../../../components/customer/EditCustomerDrawer";
import ViewFamilyMemberDrawer from "../../../components/customer/ViewFamilyMemberDrawer";
import {
  useCustomerDetails,
  useInvalidateCustomerDetails,
  type CustomerTraveller as CustomerTravellerType,
  type CustomerBooking as CustomerBookingType,
  type CustomerDetail,
} from "../../../hooks/customer-management/use-customer-details";

// Local type aliases for easier usage
type CustomerTraveller = CustomerTravellerType;
type CustomerBooking = CustomerBookingType;

const ViewCustomerPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // TanStack Query hooks
  const {
    customer,
    travellers,
    bookings,
    isLoading,
    isError,
    error,

    refetchTravellers,
  } = useCustomerDetails(id || "");

  const { invalidateAll } = useInvalidateCustomerDetails();

  // UI State
  const [showAddFamilyModal, setShowAddFamilyModal] = useState(false);
  const [showEditCustomerModal, setShowEditCustomerModal] = useState(false);
  const [showViewFamilyMemberDrawer, setShowViewFamilyMemberDrawer] =
    useState(false);
  const [selectedFamilyMember, setSelectedFamilyMember] =
    useState<CustomerTravellerType | null>(null);

  // Handle edit query parameter
  useEffect(() => {
    const editParam = searchParams.get("edit");
    if (editParam === "true") {
      setShowEditCustomerModal(true);
    }
  }, [searchParams]);

  // Handle successful family member addition
  const handleFamilyMemberAdded = () => {
    if (id) {
      refetchTravellers(); // Refresh the travellers list
    }
  };

  // Handle successful customer update
  const handleCustomerUpdated = () => {
    if (id) {
      invalidateAll(id); // Refresh all customer data
    }
    // Remove edit query parameter after successful update
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete("edit");
    setSearchParams(newSearchParams, { replace: true });
  };

  // Handle edit drawer close
  const handleEditDrawerClose = () => {
    setShowEditCustomerModal(false);
    // Remove edit query parameter when drawer is closed
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete("edit");
    setSearchParams(newSearchParams, { replace: true });
  };

  // Handle family member card click
  const handleFamilyMemberClick = (familyMember: CustomerTraveller) => {
    setSelectedFamilyMember(familyMember);
    setShowViewFamilyMemberDrawer(true);
  };

  // Handle family member update success
  const handleFamilyMemberUpdated = () => {
    if (id) {
      refetchTravellers(); // Refresh the travellers data
    }
  };

  // Handle booking click - navigate to booking detail page
  const handleBookingClick = (bookingId: string) => {
    navigate(`/hotel-management/bookings/${bookingId}`);
  };

  // Helper function to get customer display name
  const getDisplayName = (customer: CustomerDetail): string => {
    if (customer.first_name && customer.last_name) {
      return `${customer.first_name} ${customer.last_name}`;
    }
    if (customer.first_name) {
      return customer.first_name;
    }
    if (customer.last_name) {
      return customer.last_name;
    }
    return "No Name";
  };

  // Helper function to get initials for avatar
  const getInitials = (customer: CustomerDetail): string => {
    const name = getDisplayName(customer);
    if (name === "No Name") {
      return customer.email.charAt(0).toUpperCase();
    }
    return name
      .split(" ")
      .map((n) => n.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper function to format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Helper function to format date of birth
  const formatDateOfBirth = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Helper function to calculate age
  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  // Helper function to format gender
  const formatGender = (gender: string): string => {
    return gender.charAt(0).toUpperCase() + gender.slice(1);
  };

  // Helper function to format relationship
  const formatRelationship = (relationship?: string): string => {
    if (!relationship) return "Not specified";
    return (
      relationship.charAt(0).toUpperCase() +
      relationship.slice(1).replace(/_/g, " ")
    );
  };

  // Statistics calculation functions
  const getTotalBookings = (): number => {
    return bookings.length;
  };

  const getAverageRating = (): number => {
    // Hardcoded for now - in real app would come from booking reviews
    return 5.0;
  };

  const getGenderColor = (gender: string) => {
    switch (gender) {
      case "male":
        return "bg-blue-100 text-blue-800";
      case "female":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-purple-100 text-purple-800";
    }
  };

  // Helper function to format currency`
  const formatCurrency = (amount: number, currency: string): string => {
    // Default to USD if currency is not provided
    const currencyCode = currency?.toUpperCase() || 'USD';
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  };

  // Helper function to calculate nights between dates
  const calculateNights = (checkIn: string, checkOut: string): number => {
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    const timeDiff = checkOutDate.getTime() - checkInDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  };

  // Copy functions
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${type} copied to clipboard!`);
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error(`Failed to copy ${type.toLowerCase()}`);
    }
  };

  const copyEmail = () => {
    if (customer?.email) {
      copyToClipboard(customer.email, "Email");
    }
  };

  const copyPhone = () => {
    if (customer?.phone) {
      copyToClipboard(customer.phone, "Phone number");
    }
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <div className="flex items-center justify-between mb-1">
        {/* Back Button */}
        <Button
          variant="secondary"
          size="small"
          onClick={() => navigate("/all-customers")}
          className="p-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            className="flex items-center space-x-2 px-4 py-2"
            onClick={() => setShowEditCustomerModal(true)}
          >
            <Edit className="h-4 w-4" />
            <span>Edit Profile</span>
          </Button>
          <Button
            variant="primary"
            className="flex items-center space-x-2 px-4 py-2"
            onClick={() =>
              navigate(`/hotel-management/bookings/create?customerId=${id}`)
            }
          >
            <Bed className="h-4 w-4" />
            <span>New Booking</span>
          </Button>
        </div>
      </div>

      <Container className="">
        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <Text className="text-muted-foreground">
              Loading customer details...
            </Text>
          </div>
        )}

        {/* Error State */}
        {isError && error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <Text className="text-destructive">
              Error:{" "}
              {error instanceof Error
                ? error.message
                : "Failed to load customer"}
            </Text>
          </div>
        )}

        {/* Modern Customer Profile */}
        {!isLoading && !isError && customer && (
          <div className="space-y-6">
            {/* Profile Header Card */}
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <div className="flex items-center space-x-4">
                {/* Avatar */}
                <div className="h-16 w-16 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 text-white font-bold flex items-center justify-center text-xl shadow-lg">
                  {getInitials(customer)}
                </div>

                {/* Customer Name and Contact Info */}
                <div className="flex-1">
                  <Heading
                    level="h1"
                    className="text-2xl font-bold text-gray-900 dark:text-white mb-2"
                  >
                    {getDisplayName(customer)}
                  </Heading>

                  {/* Contact Information in horizontal layout */}
                  <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center space-x-2 group">
                      <Mail className="h-4 w-4" />
                      <span>{customer.email}</span>
                      <button
                        onClick={copyEmail}
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                        title="Copy email"
                      >
                        <Copy className="h-3 w-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300" />
                      </button>
                    </div>
                    {customer.phone && (
                      <div className="flex items-center space-x-2 group">
                        <Phone className="h-4 w-4" />
                        <span>{customer.phone}</span>
                        <button
                          onClick={copyPhone}
                          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                          title="Copy phone number"
                        >
                          <Copy className="h-3 w-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300" />
                        </button>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span>Last visit: {bookings.length > 0 ? formatDate(bookings.reduce((latest, booking) => new Date(booking.check_out_date) > new Date(latest.check_out_date) ? booking : latest).check_out_date).split(",")[0] : "Never"}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Divider */}
              <div className="border-t border-gray-200 dark:border-gray-700 my-4"></div>

              {/* Member Since and Total Bookings Row */}
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-orange-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Member since <span className="font-medium text-gray-900 dark:text-white">{formatDate(customer.created_at).split(",")[0]}</span>
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Total bookings <span className="font-medium text-gray-900 dark:text-white">{bookings.length}</span>
                  </span>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="">
              <Tabs defaultValue="family" className="w-full">
                <Tabs.List className="px-4">
                  <Tabs.Trigger
                    value="family"
                    className="flex items-center gap-2"
                  >
                    <Users className="h-4 w-4" />
                    Family Members
                  </Tabs.Trigger>
                  <Tabs.Trigger
                    value="bookings"
                    className="flex items-center gap-2"
                  >
                    <Bed className="h-4 w-4" />
                    Past Bookings
                  </Tabs.Trigger>
                </Tabs.List>

                <div className="mt-2">
                  <Tabs.Content value="family">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <Heading
                            level="h2"
                            className="text-xl font-bold text-gray-900 dark:text-white"
                          >
                            Family Members
                          </Heading>
                          <Text className="text-gray-600 dark:text-gray-400 mt-1 text-sm">
                            Manage family members and traveling companions
                          </Text>
                        </div>
                        <Button
                          variant="primary"
                          className="flex items-center space-x-2 px-4 py-2"
                          onClick={() => setShowAddFamilyModal(true)}
                        >
                          <UserPlus className="h-4 w-4" />
                          <span>Add Family Member</span>
                        </Button>
                      </div>

                      {/* Family Members Content */}
                      {travellers.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {travellers.map((traveller) => (
                            <div
                              key={traveller.id}
                              className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-blue-300 transition-all duration-200 cursor-pointer"
                              onClick={() => handleFamilyMemberClick(traveller)}
                            >
                              <div className="flex items-start justify-between mb-3">
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-lg font-bold uppercase">
                                    {traveller.first_name.charAt(0)}
                                    {traveller.last_name.charAt(0)}
                                  </div>
                                  <div>
                                    <Text className="font-semibold text-gray-900 dark:text-white text-base">
                                      {traveller.first_name}{" "}
                                      {traveller.last_name}
                                    </Text>
                                    <Text className="text-xs text-gray-600 dark:text-gray-400">
                                      {formatRelationship(
                                        traveller.relationship
                                      )}{" "}
                                      • {calculateAge(traveller.date_of_birth)}{" "}
                                      years old
                                    </Text>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <Badge className={`px-2 py-1 rounded-full text-xs ${getGenderColor(traveller.gender)}`}>
                                  {formatGender(traveller.gender)}
                                </Badge>
                                <Text className="text-gray-500 dark:text-gray-400 text-xs">
                                  {formatDateOfBirth(traveller.date_of_birth)}
                                </Text>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Users className="h-8 w-8 text-blue-600" />
                          </div>
                          <Heading
                            level="h3"
                            className="text-lg font-semibold text-gray-900 dark:text-white mb-2"
                          >
                            No family members yet
                          </Heading>
                          <Text className="text-gray-600 dark:text-gray-400 mb-4 max-w-md mx-auto text-sm">
                            Add family members to help with booking management
                            and travel planning
                          </Text>
                          <Button
                            variant="primary"
                            className="flex items-center space-x-2 mx-auto px-4 py-2"
                            onClick={() => setShowAddFamilyModal(true)}
                          >
                            <UserPlus className="h-4 w-4" />
                            <span>Add First Family Member</span>
                          </Button>
                        </div>
                      )}
                    </div>
                  </Tabs.Content>

                  <Tabs.Content value="bookings">
                    <div className="p-6">
                      <div className="mb-6">
                        <Heading
                          level="h2"
                          className="text-xl font-bold text-gray-900 dark:text-white"
                        >
                          Past Bookings
                        </Heading>
                        <Text className="text-gray-600 dark:text-gray-400 mt-1 text-sm">
                          View all previous bookings and reservations
                        </Text>
                      </div>
                      {bookings.length > 0 ? (
                        <div className="space-y-4">
                          {bookings.map((booking) => (
                            <div
                              key={booking.id}
                              className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-blue-300 transition-all duration-200 cursor-pointer"
                              onClick={() => handleBookingClick(booking.id)}
                            >
                              <div className="flex items-start justify-between mb-3">
                                <div>
                                  <Text className="font-semibold text-gray-900 dark:text-white text-base">
                                    {booking.hotel_name}
                                  </Text>
                                  <Text className="text-xs text-gray-600 dark:text-gray-400">
                                    Room • {calculateNights(booking.check_in_date, booking.check_out_date)} nights
                                  </Text>
                                </div>
                                <Badge
                                  className={`px-2 py-0.5 rounded-full text-xs ${
                                    booking.status === "confirmed"
                                      ? "bg-green-100 text-green-800"
                                      : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {booking.status}
                                </Badge>
                              </div>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                                <div>
                                  <Text className="text-gray-500 text-xs">
                                    Check-in
                                  </Text>
                                  <Text className="font-medium text-sm">
                                    {
                                      formatDate(booking.check_in_date).split(
                                        ","
                                      )[0]
                                    }
                                  </Text>
                                </div>
                                <div>
                                  <Text className="text-gray-500 text-xs">
                                    Check-out
                                  </Text>
                                  <Text className="font-medium text-sm">
                                    {
                                      formatDate(booking.check_out_date).split(
                                        ","
                                      )[0]
                                    }
                                  </Text>
                                </div>
                                <div>
                                  <Text className="text-gray-500 text-xs">
                                    Guests
                                  </Text>
                                  <Text className="font-medium text-sm">-</Text>
                                </div>
                                <div>
                                  <Text className="text-gray-500 text-xs">
                                    Total
                                  </Text>
                                  <Text className="font-medium text-sm">
                                    {formatCurrency(
                                      booking.total_amount,
                                      booking.currency
                                    )}
                                  </Text>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Bed className="h-8 w-8 text-blue-600" />
                          </div>
                          <Heading
                            level="h3"
                            className="text-lg font-semibold text-gray-900 dark:text-white mb-2"
                          >
                            No bookings yet
                          </Heading>
                          <Text className="text-gray-600 dark:text-gray-400 text-sm">
                            Customer bookings will appear here
                          </Text>
                        </div>
                      )}
                    </div>
                  </Tabs.Content>
                </div>
              </Tabs>
            </div>
          </div>
        )}

        {/* Customer Not Found */}
        {!isLoading && !isError && !customer && (
          <div className="text-center py-12">
            <Text className="text-muted-foreground">Customer not found</Text>
            <Button
              variant="secondary"
              className="mt-4"
              onClick={() => navigate("/all-customers")}
            >
              Back to Customers
            </Button>
          </div>
        )}
      </Container>

      {/* Add Family Member Drawer */}
      {customer && (
        <AddFamilyMemberDrawer
          isOpen={showAddFamilyModal}
          onClose={() => setShowAddFamilyModal(false)}
          customerId={customer.id}
          onSuccess={handleFamilyMemberAdded}
        />
      )}

      {/* Edit Customer Drawer */}
      {customer && (
        <EditCustomerDrawer
          isOpen={showEditCustomerModal}
          onClose={handleEditDrawerClose}
          customer={customer}
          onSuccess={handleCustomerUpdated}
        />
      )}

      {/* View Family Member Drawer */}
      <ViewFamilyMemberDrawer
        isOpen={showViewFamilyMemberDrawer}
        onClose={() => setShowViewFamilyMemberDrawer(false)}
        familyMember={selectedFamilyMember}
        onSuccess={handleFamilyMemberUpdated}
      />
    </>
  );
};

export const config = defineRouteConfig({
  label: "View Customer",
  icon: User,
});

export default ViewCustomerPage;
