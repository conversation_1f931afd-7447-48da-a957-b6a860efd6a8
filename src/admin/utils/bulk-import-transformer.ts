/**
 * Utility functions to transform Excel/CSV data into the format expected by existing bulk pricing APIs
 */

/**
 * Helper function to normalize meal plan names
 * Treats "-", "No Meals", "No Meal Plan", empty strings as null
 */
const normalizeMealPlanName = (
  mealPlanName: string | undefined | null
): string | null => {
  if (!mealPlanName || typeof mealPlanName !== "string") return null;

  const normalized = mealPlanName.trim().toLowerCase();

  // Common variations that mean "no meal plan"
  // NOTE: "No Meals" is a real meal plan name, not a null indicator
  const noMealPlanVariations = ["-", "none", "n/a", "na", ""];

  if (noMealPlanVariations.includes(normalized)) {
    return null;
  }

  return mealPlanName.trim();
};

/**
 * Convert Excel serial date number to YYYY-MM-DD string format
 * Excel stores dates as serial numbers (days since 1900-01-01)
 */
const convertExcelDateToString = (
  excelDate: string | number | undefined | null
): string | null => {
  if (!excelDate) return null;

  // If it's already a proper date string, return it
  if (typeof excelDate === "string" && excelDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
    return excelDate;
  }

  // Convert to number for Excel serial date conversion
  const serialNumber =
    typeof excelDate === "string" ? parseFloat(excelDate) : excelDate;

  if (isNaN(serialNumber) || serialNumber <= 0) {
    return null;
  }

  // Excel epoch starts at 1900-01-01, but Excel incorrectly treats 1900 as a leap year
  // So we need to account for this bug in Excel's date system
  const excelEpoch = new Date(1899, 11, 30); // December 30, 1899
  const millisecondsPerDay = 24 * 60 * 60 * 1000;

  const resultDate = new Date(
    excelEpoch.getTime() + serialNumber * millisecondsPerDay
  );

  // Format as YYYY-MM-DD
  const year = resultDate.getFullYear();
  const month = String(resultDate.getMonth() + 1).padStart(2, "0");
  const day = String(resultDate.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

// Test the Excel date conversion
console.log("[Bulk Import Transform] Excel date conversion test:", {
  "45860": convertExcelDateToString("45860"), // Should be 2025-07-22
  "45867": convertExcelDateToString("45867"), // Should be 2025-07-29
});

export type ExcelRowData = {
  room_config_name: string;
  occupancy_name: string;
  meal_plan_name: string;
  currency_code: string;
  pricing_type?: string; // 'Base Pricing' or 'Seasonal Pricing'
  seasonal_period?: string;
  seasonal_start_date?: string;
  seasonal_end_date?: string;

  // Weekday prices
  monday_price?: number;
  tuesday_price?: number;
  wednesday_price?: number;
  thursday_price?: number;
  friday_price?: number;
  saturday_price?: number;
  sunday_price?: number;

  // Default cost/margin values
  default_gross_cost?: number;
  default_fixed_margin?: number;
  default_margin_percentage?: number;

  // Weekday-specific cost/margin values
  monday_gross_cost?: number;
  monday_fixed_margin?: number;
  monday_margin_percentage?: number;
  tuesday_gross_cost?: number;
  tuesday_fixed_margin?: number;
  tuesday_margin_percentage?: number;
  wednesday_gross_cost?: number;
  wednesday_fixed_margin?: number;
  wednesday_margin_percentage?: number;
  thursday_gross_cost?: number;
  thursday_fixed_margin?: number;
  thursday_margin_percentage?: number;
  friday_gross_cost?: number;
  friday_fixed_margin?: number;
  friday_margin_percentage?: number;
  saturday_gross_cost?: number;
  saturday_fixed_margin?: number;
  saturday_margin_percentage?: number;
  sunday_gross_cost?: number;
  sunday_fixed_margin?: number;
  sunday_margin_percentage?: number;
};

export type TransformedImportData = {
  baseRules: Record<string, BasePricingPayload>;
  seasonalRules: Record<string, SeasonalPricingPayload>;
};

export type BasePricingPayload = {
  currency_code: string;
  weekday_rules: WeekdayRule[];
};

export type SeasonalPricingPayload = {
  currency_code: string;
  name: string;
  start_date: string;
  end_date: string;
  weekday_rules: WeekdayRule[];
};

export type WeekdayRule = {
  occupancy_type_id: string;
  meal_plan_id: string | null;
  default_values: {
    gross_cost: number;
    fixed_margin: number;
    margin_percentage: number;
    total: number;
  };
  weekday_prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  weekday_values: {
    mon: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
    tue: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
    wed: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
    thu: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
    fri: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
    sat: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
    sun: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
  };
};

/**
 * Transform Excel data to API format for bulk import
 */
export const transformExcelDataToApiFormat = (
  excelData: ExcelRowData[],
  roomConfigs: any[],
  occupancyConfigs: any[],
  mealPlans: any[],
  seasonalPeriods: any[]
): TransformedImportData => {
  console.log("[Bulk Import Transform] Starting transformation...");
  console.log("[Bulk Import Transform] Input data:", {
    excelRowCount: excelData.length,
    roomConfigCount: roomConfigs.length,
    occupancyConfigCount: occupancyConfigs.length,
    mealPlanCount: mealPlans.length,
    seasonalPeriodCount: seasonalPeriods.length,
  });

  const baseRules: Record<string, BasePricingPayload> = {};
  const seasonalRules: Record<string, SeasonalPricingPayload> = {};

  // Group data by room config and pricing type
  excelData.forEach((row, index) => {
    console.log(`[Bulk Import Transform] Processing row ${index + 1}:`, row);
    // Find matching entities
    const roomConfig = roomConfigs.find(
      (rc) => rc.title === row.room_config_name
    );
    const occupancyConfig = occupancyConfigs.find(
      (oc) => oc.name === row.occupancy_name
    );
    // Handle meal plan: normalize and find matching meal plan
    const normalizedMealPlanName = normalizeMealPlanName(row.meal_plan_name);
    const mealPlan = normalizedMealPlanName
      ? mealPlans.find((mp) => mp.name === normalizedMealPlanName)
      : null;

    console.log(`[Bulk Import Transform] Row ${index + 1} entity resolution:`, {
      roomConfig: roomConfig
        ? { id: roomConfig.id, title: roomConfig.title }
        : null,
      occupancyConfig: occupancyConfig
        ? { id: occupancyConfig.id, name: occupancyConfig.name }
        : null,
      originalMealPlan: row.meal_plan_name,
      normalizedMealPlan: normalizedMealPlanName,
      mealPlan: mealPlan ? { id: mealPlan.id, name: mealPlan.name } : null,
    });

    if (!roomConfig || !occupancyConfig) {
      console.warn(
        `[Bulk Import Transform] Skipping row ${
          index + 1
        }: Room config or occupancy config not found`,
        row
      );
      return;
    }

    // Determine if this is base or seasonal pricing
    // FIXED: Only treat as seasonal if explicitly marked as seasonal pricing
    // "Base Price" in seasonal_period should NOT trigger seasonal pricing
    const isSeasonalPricing =
      row.pricing_type === "Seasonal Pricing" ||
      (row.seasonal_period &&
        row.seasonal_period !== "Base Price" &&
        row.seasonal_period !== "Base Pricing") ||
      row.seasonal_start_date ||
      row.seasonal_end_date;

    console.log(
      `[Bulk Import Transform] Row ${index + 1} pricing type determination:`,
      {
        pricing_type: row.pricing_type,
        seasonal_period: row.seasonal_period,
        seasonal_start_date: row.seasonal_start_date,
        seasonal_end_date: row.seasonal_end_date,
        isSeasonalPricing: isSeasonalPricing,
        decision: isSeasonalPricing ? "SEASONAL" : "BASE",
      }
    );

    // Create weekday rule
    console.log(
      `[Bulk Import Transform] Row ${index + 1} creating weekday rule for:`,
      {
        occupancy_type_id: occupancyConfig.id,
        meal_plan_id: mealPlan?.id || null,
        isSeasonalPricing,
        pricing_type: row.pricing_type,
        note: "Sending display values directly - backend handles currency conversion",
      }
    );

    const weekdayRule: WeekdayRule = {
      occupancy_type_id: occupancyConfig.id,
      meal_plan_id: mealPlan?.id || null,
      default_values: {
        gross_cost: parseFloat(row.default_gross_cost?.toString() || "0") || 0,
        fixed_margin:
          parseFloat(row.default_fixed_margin?.toString() || "0") || 0,
        margin_percentage:
          parseFloat(row.default_margin_percentage?.toString() || "0") || 0,
        total: 0, // Will be calculated by backend
      },
      weekday_prices: {
        mon: parseFloat(row.monday_price?.toString() || "0") || 0,
        tue: parseFloat(row.tuesday_price?.toString() || "0") || 0,
        wed: parseFloat(row.wednesday_price?.toString() || "0") || 0,
        thu: parseFloat(row.thursday_price?.toString() || "0") || 0,
        fri: parseFloat(row.friday_price?.toString() || "0") || 0,
        sat: parseFloat(row.saturday_price?.toString() || "0") || 0,
        sun: parseFloat(row.sunday_price?.toString() || "0") || 0,
      },
      weekday_values: {
        mon: {
          gross_cost: parseFloat(row.monday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.monday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.monday_margin_percentage?.toString() || "0") || 0,
        },
        tue: {
          gross_cost:
            parseFloat(row.tuesday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.tuesday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.tuesday_margin_percentage?.toString() || "0") || 0,
        },
        wed: {
          gross_cost:
            parseFloat(row.wednesday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.wednesday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.wednesday_margin_percentage?.toString() || "0") || 0,
        },
        thu: {
          gross_cost:
            parseFloat(row.thursday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.thursday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.thursday_margin_percentage?.toString() || "0") || 0,
        },
        fri: {
          gross_cost: parseFloat(row.friday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.friday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.friday_margin_percentage?.toString() || "0") || 0,
        },
        sat: {
          gross_cost:
            parseFloat(row.saturday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.saturday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.saturday_margin_percentage?.toString() || "0") || 0,
        },
        sun: {
          gross_cost: parseFloat(row.sunday_gross_cost?.toString() || "0") || 0,
          fixed_margin:
            parseFloat(row.sunday_fixed_margin?.toString() || "0") || 0,
          margin_percentage:
            parseFloat(row.sunday_margin_percentage?.toString() || "0") || 0,
        },
      },
    };

    if (isSeasonalPricing) {
      // Handle seasonal pricing - try to match existing seasonal periods first
      const excelStartDate = convertExcelDateToString(row.seasonal_start_date);
      const excelEndDate = convertExcelDateToString(row.seasonal_end_date);

      console.log(
        `[Bulk Import Transform] Row ${
          index + 1
        } looking for existing seasonal period:`,
        {
          excelSeasonalPeriod: row.seasonal_period,
          excelStartDate,
          excelEndDate,
          availableSeasonalPeriods: seasonalPeriods.map((sp) => ({
            id: sp.id,
            name: sp.name,
            start_date: sp.start_date,
            end_date: sp.end_date,
          })),
        }
      );

      // Try to find matching existing seasonal period by date range first, then by name
      let matchingSeasonalPeriod = null;

      if (excelStartDate && excelEndDate) {
        // Match by date range (exact match)
        matchingSeasonalPeriod = seasonalPeriods.find(
          (sp) =>
            sp.start_date === excelStartDate && sp.end_date === excelEndDate
        );

        if (matchingSeasonalPeriod) {
          console.log(
            `[Bulk Import Transform] Found seasonal period by date match:`,
            matchingSeasonalPeriod
          );
        }
      }

      // If no date match, try to match by name (more flexible matching)
      if (!matchingSeasonalPeriod && row.seasonal_period) {
        // Try exact name match first (case-insensitive)
        matchingSeasonalPeriod = seasonalPeriods.find(
          (sp) => sp.name.toLowerCase() === row.seasonal_period.toLowerCase()
        );

        // If no exact match, try partial matching
        if (!matchingSeasonalPeriod) {
          matchingSeasonalPeriod = seasonalPeriods.find(
            (sp) =>
              sp.name
                .toLowerCase()
                .includes(row.seasonal_period.toLowerCase()) ||
              row.seasonal_period.toLowerCase().includes(sp.name.toLowerCase())
          );
        }

        if (matchingSeasonalPeriod) {
          console.log(
            `[Bulk Import Transform] Found seasonal period by name match:`,
            matchingSeasonalPeriod
          );
        }
      }

      // Use existing seasonal period name if found, otherwise use Excel data
      const seasonalPeriodName =
        matchingSeasonalPeriod?.name ||
        row.seasonal_period ||
        "Imported Season";
      const seasonalKey = `${roomConfig.id}|||${seasonalPeriodName}`;

      console.log(
        `[Bulk Import Transform] Row ${index + 1} seasonal period resolution:`,
        {
          seasonalKey,
          finalSeasonalPeriodName: seasonalPeriodName,
          matchedExistingPeriod: !!matchingSeasonalPeriod,
          matchedPeriodId: matchingSeasonalPeriod?.id,
          start_date: excelStartDate,
          end_date: excelEndDate,
        }
      );

      if (!seasonalRules[seasonalKey]) {
        // Use matched seasonal period dates if available, otherwise convert Excel dates
        const startDate =
          matchingSeasonalPeriod?.start_date ||
          convertExcelDateToString(row.seasonal_start_date) ||
          new Date().toISOString().split("T")[0];
        const endDate =
          matchingSeasonalPeriod?.end_date ||
          convertExcelDateToString(row.seasonal_end_date) ||
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0];

        console.log(`[Bulk Import Transform] Final seasonal period data:`, {
          usingExistingPeriod: !!matchingSeasonalPeriod,
          originalStartDate: row.seasonal_start_date,
          originalEndDate: row.seasonal_end_date,
          finalStartDate: startDate,
          finalEndDate: endDate,
          finalName: seasonalPeriodName,
        });

        seasonalRules[seasonalKey] = {
          currency_code: row.currency_code,
          name: seasonalPeriodName,
          start_date: startDate,
          end_date: endDate,
          // Include seasonal_period_id if we matched an existing period
          ...(matchingSeasonalPeriod?.id && {
            seasonal_period_id: matchingSeasonalPeriod.id,
          }),
          weekday_rules: [],
        };

        console.log(
          `[Bulk Import Transform] Created new seasonal rule group:`,
          seasonalRules[seasonalKey]
        );
      }

      // Validate weekday rule before adding
      console.log(
        `[Bulk Import Transform] Adding weekday rule to seasonal group:`,
        {
          seasonalKey,
          weekdayRule: {
            occupancy_type_id: weekdayRule.occupancy_type_id,
            meal_plan_id: weekdayRule.meal_plan_id,
            default_values: weekdayRule.default_values,
            weekday_prices: weekdayRule.weekday_prices,
            weekday_values_sample: {
              mon: weekdayRule.weekday_values.mon,
              tue: weekdayRule.weekday_values.tue,
            },
          },
        }
      );

      seasonalRules[seasonalKey].weekday_rules.push(weekdayRule);
    } else {
      // Handle base pricing
      const baseKey = roomConfig.id;

      console.log(
        `[Bulk Import Transform] Row ${index + 1} creating base pricing:`,
        {
          baseKey,
          roomConfigTitle: roomConfig.title,
        }
      );

      if (!baseRules[baseKey]) {
        baseRules[baseKey] = {
          currency_code: row.currency_code,
          weekday_rules: [],
        };

        console.log(
          `[Bulk Import Transform] Created new base rule group:`,
          baseRules[baseKey]
        );
      }

      baseRules[baseKey].weekday_rules.push(weekdayRule);
    }
  });

  console.log("[Bulk Import Transform] Transformation complete!");
  console.log("[Bulk Import Transform] Final base rules:", {
    count: Object.keys(baseRules).length,
    roomConfigIds: Object.keys(baseRules),
    details: baseRules,
  });
  console.log("[Bulk Import Transform] Final seasonal rules:", {
    count: Object.keys(seasonalRules).length,
    seasonalKeys: Object.keys(seasonalRules),
    details: seasonalRules,
  });

  return { baseRules, seasonalRules };
};

/**
 * Execute bulk import using existing APIs
 */
export const executeBulkImport = async (
  transformedData: TransformedImportData,
  onProgress?: (progress: number) => void
): Promise<{ success: boolean; results: any[]; errors: any[] }> => {
  const results: any[] = [];
  const errors: any[] = [];

  const totalOperations =
    Object.keys(transformedData.baseRules).length +
    Object.keys(transformedData.seasonalRules).length;
  let completedOperations = 0;

  try {
    // Import base pricing rules
    for (const [roomConfigId, payload] of Object.entries(
      transformedData.baseRules
    )) {
      try {
        console.log(
          `[Bulk Import API] Importing base pricing for room config: ${roomConfigId}`
        );
        console.log(
          `[Bulk Import API] Base pricing payload:`,
          JSON.stringify(payload, null, 2)
        );

        // Base pricing API only needs currency_code and weekday_rules
        const basePricingPayload = {
          currency_code: payload.currency_code,
          weekday_rules: payload.weekday_rules,
        };

        console.log(
          `[Bulk Import API] Corrected base pricing payload:`,
          JSON.stringify(basePricingPayload, null, 2)
        );

        const response = await fetch(
          `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(basePricingPayload),
          }
        );

        console.log(
          `[Bulk Import API] Base pricing response status: ${response.status} ${response.statusText}`
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `[Bulk Import API] Base pricing API error for room ${roomConfigId}:`,
            {
              status: response.status,
              statusText: response.statusText,
              errorBody: errorText,
              requestPayload: basePricingPayload,
            }
          );
          throw new Error(
            `Failed to import base pricing for room ${roomConfigId}: ${response.status} ${response.statusText} - ${errorText}`
          );
        }

        const result = await response.json();
        console.log(
          `[Bulk Import API] Base pricing API success for room ${roomConfigId}:`,
          result
        );
        results.push({ type: "base", roomConfigId, result });

        completedOperations++;
        onProgress?.(Math.round((completedOperations / totalOperations) * 100));
      } catch (error) {
        console.error(
          `Error importing base pricing for room ${roomConfigId}:`,
          error
        );
        errors.push({
          type: "base",
          roomConfigId,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Import seasonal pricing rules
    for (const [seasonalKey, payload] of Object.entries(
      transformedData.seasonalRules
    )) {
      try {
        const roomConfigId = seasonalKey.split("|||")[0];
        console.log(`[Bulk Import API] Seasonal key parsing:`, {
          seasonalKey,
          extractedRoomConfigId: roomConfigId,
          seasonName: payload.name,
        });
        console.log(
          `[Bulk Import API] Importing seasonal pricing for room config: ${roomConfigId}, season: ${payload.name}`
        );
        console.log(
          `[Bulk Import API] Seasonal pricing payload:`,
          JSON.stringify(payload, null, 2)
        );

        const response = await fetch(
          `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing/bulk`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
          }
        );

        console.log(
          `[Bulk Import API] Seasonal pricing response status: ${response.status} ${response.statusText}`
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `[Bulk Import API] Seasonal pricing API error for room ${roomConfigId}:`,
            {
              status: response.status,
              statusText: response.statusText,
              errorBody: errorText,
              requestPayload: payload,
            }
          );
          throw new Error(
            `Failed to import seasonal pricing for room ${roomConfigId}: ${response.status} ${response.statusText} - ${errorText}`
          );
        }

        const result = await response.json();
        console.log(
          `[Bulk Import API] Seasonal pricing API success for room ${roomConfigId}:`,
          result
        );
        results.push({
          type: "seasonal",
          roomConfigId,
          seasonName: payload.name,
          result,
        });

        completedOperations++;
        onProgress?.(Math.round((completedOperations / totalOperations) * 100));
      } catch (error) {
        console.error(
          `Error importing seasonal pricing for ${seasonalKey}:`,
          error
        );
        errors.push({
          type: "seasonal",
          seasonalKey,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors,
    };
  } catch (error) {
    console.error("Bulk import failed:", error);
    return {
      success: false,
      results,
      errors: [
        ...errors,
        {
          type: "general",
          error: error instanceof Error ? error.message : "Unknown error",
        },
      ],
    };
  }
};

/**
 * Validate Excel data before transformation
 */
export const validateExcelData = (
  excelData: ExcelRowData[],
  roomConfigs: any[],
  occupancyConfigs: any[],
  mealPlans: any[]
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  excelData.forEach((row, index) => {
    const rowNumber = index + 1;

    // Check required fields
    if (!row.room_config_name) {
      errors.push(`Row ${rowNumber}: Room config name is required`);
    }
    if (!row.occupancy_name) {
      errors.push(`Row ${rowNumber}: Occupancy name is required`);
    }
    if (!row.meal_plan_name) {
      errors.push(`Row ${rowNumber}: Meal plan name is required`);
    }
    if (!row.currency_code) {
      errors.push(`Row ${rowNumber}: Currency code is required`);
    }

    // Validate entities exist
    if (
      row.room_config_name &&
      !roomConfigs.find((rc) => rc.title === row.room_config_name)
    ) {
      errors.push(
        `Row ${rowNumber}: Room config "${row.room_config_name}" not found`
      );
    }
    if (
      row.occupancy_name &&
      !occupancyConfigs.find((oc) => oc.name === row.occupancy_name)
    ) {
      errors.push(
        `Row ${rowNumber}: Occupancy config "${row.occupancy_name}" not found`
      );
    }
    // Validate meal plan exists (normalize meal plan name first)
    const normalizedMealPlanName = normalizeMealPlanName(row.meal_plan_name);
    if (
      normalizedMealPlanName &&
      !mealPlans.find((mp) => mp.name === normalizedMealPlanName)
    ) {
      errors.push(
        `Row ${rowNumber}: Meal plan "${row.meal_plan_name}" not found`
      );
    }

    // Validate pricing data exists (either prices or cost/margin)
    const hasPrices = [
      row.monday_price,
      row.tuesday_price,
      row.wednesday_price,
      row.thursday_price,
      row.friday_price,
      row.saturday_price,
      row.sunday_price,
    ].some((price) => price && parseFloat(price.toString()) > 0);

    const hasCostMargin =
      row.default_gross_cost ||
      [
        row.monday_gross_cost,
        row.tuesday_gross_cost,
        row.wednesday_gross_cost,
        row.thursday_gross_cost,
        row.friday_gross_cost,
        row.saturday_gross_cost,
        row.sunday_gross_cost,
      ].some((cost) => cost && parseFloat(cost.toString()) > 0);

    // if (!hasPrices && !hasCostMargin) {
    //   errors.push(
    //     `Row ${rowNumber}: Must have either weekday prices or cost/margin data`
    //   );
    // }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};
